<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="description" content="">
	<meta name="author" content="">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
	<title>WD POINTCLOUD Viewer</title>

	<link rel="stylesheet" type="text/css" href="./libs/potree/potree.css">
	<link rel="stylesheet" type="text/css" href="./libs/jquery-ui/jquery-ui.min.css">
	<link rel="stylesheet" type="text/css" href="./libs/openlayers3/ol.css">
	<link rel="stylesheet" type="text/css" href="./libs/spectrum/spectrum.css">
	<link rel="stylesheet" type="text/css" href="./libs/jstree/themes/mixed/style.css">
</head>

<body>
	<script src="./libs/jquery/jquery-3.1.1.min.js"></script>
	<script src="./libs/spectrum/spectrum.js"></script>
	<script src="./libs/jquery-ui/jquery-ui.min.js"></script>
	<script src="./libs/other/BinaryHeap.js"></script>
	<script src="./libs/tween/tween.min.js"></script>
	<script src="./libs/d3/d3.js"></script>
	<script src="./libs/proj4/proj4.js"></script>
	<script src="./libs/openlayers3/ol.js"></script>
	<script src="./libs/i18next/i18next.js"></script>
	<script src="./libs/jstree/jstree.js"></script>
	<script src="./libs/potree/potree.js"></script>
	<script src="./libs/plasio/js/laslaz.js"></script>
	
	<!-- add library for shapefile -->
	<script src="./libs/shapefile/shapefile.js"></script>
	
	<!-- INCLUDE ADDITIONAL DEPENDENCIES HERE -->
	<!-- INCLUDE SETTINGS HERE -->
	
	<div class="potree_container" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; ">
		<div id="potree_render_area" style="background-image: url('../build/potree/resources/images/background.jpg');"></div>
		<div id="potree_sidebar_container"> </div>
	</div>
	
	<script>
	
		window.viewer = new Potree.Viewer(document.getElementById("potree_render_area"));
		
		viewer.setEDLEnabled(true);
		viewer.setFOV(60);
		viewer.setPointBudget(2_000_000);
		<!-- INCLUDE SETTINGS HERE -->
		viewer.loadSettingsFromURL();
		
		viewer.setDescription("");
		
		viewer.loadGUI(() => {
			viewer.setLanguage('en');
			$("#menu_appearance").next().show();
			$("#menu_tools").next().show();
			$("#menu_clipping").next().show();
			viewer.toggleSidebar();
		});
		
		

		Potree.loadPointCloud("./pointclouds/index/metadata.json", "index", e => {
			let scene = viewer.scene;
			let pointcloud = e.pointcloud;
			
			let material = pointcloud.material;
			material.size = 1;
			material.pointSizeType = Potree.PointSizeType.ADAPTIVE;
			material.shape = Potree.PointShape.SQUARE;
			material.activeAttributeName = "rgba";
			
			
			// shapefile loading
			setTimeout(() => {
				loadShapefile();
			}, 1000);

			function loadShapefile() {
				let url = "./shp/bu.shp"; // replace with your shapefile path
				shapefile.open(url)
					.then(source => source.read()
						.then(function log(feature) {
							if (feature.done) return;
							console.log(feature.value);
							// Here you can process the feature, e.g., add it to the scene
							// For example, you could create a Potree.Shape from the feature.geometry
							return source.read().then(log);
						}))
					.catch(error => console.error(error));
			}
			
			// add shapefile to scene
			function addShapefileToScene() {
				let url = "./shp/bu.shp"; // replace with your shapefile path
				shapefile.open(url)
					.then(source => source.read()
						.then(function log(feature) {
							if (feature.done) return;
							console.log(feature.value);
							// Here you can process the feature, e.g., add it to the scene
							// For example, you could create a Potree.Shape from the feature.geometry
							return source.read().then(log);
						}))
					.catch(error => console.error(error));
			}

			

			
			scene.addPointCloud(pointcloud);
			
			viewer.fitToScreen();
		});

		
		
	</script>
	
	
  </body>
</html>
